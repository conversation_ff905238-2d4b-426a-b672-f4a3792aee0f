{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:19.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:25.537Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:31.581Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:37.741Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:43.855Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:49.850Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:55.731Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:12.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:44.343Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:10.917Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:17.167Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:23.050Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:29.301Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:35.312Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:17.143Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:37.398Z"}
