{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:19.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:25.537Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:31.581Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:37.741Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:43.855Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:49.850Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:55.731Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:12.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:44.343Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:10.917Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:17.167Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:23.050Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:29.301Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:35.312Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:17.143Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:37.398Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:37:37.744Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:40:37.174Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:41:01.971Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:49:51.793Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:10.019Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:15.950Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:22.019Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:28.078Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:46.771Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:53.994Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:11.339Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:17.305Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:23.426Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:29.290Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:35.227Z"}
{"level":"error","message":"Error parsing sentiment response: Unexpected non-whitespace character after JSON at position 3263 (line 32 column 1)","service":"trading-bot","stack":"SyntaxError: Unexpected non-whitespace character after JSON at position 3263 (line 32 column 1)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:51.306Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:02:57.846Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:18.182Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:39.145Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:56.729Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:06.397Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:12.693Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:18.950Z"}
