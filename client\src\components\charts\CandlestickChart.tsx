import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, CandlestickData, LineData, HistogramData } from 'lightweight-charts';
import { OHLCV, AdvancedTechnicalIndicators } from '../../types';

interface CandlestickChartProps {
  data: OHLCV[];
  indicators?: AdvancedTechnicalIndicators;
  height?: number;
  showVolume?: boolean;
  showIndicators?: boolean;
  ticker: string;
}

export const CandlestickChart: React.FC<CandlestickChartProps> = ({
  data,
  indicators,
  height = 600,
  showVolume = true,
  showIndicators = true,
  ticker
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to extract timestamp from OHLCV data
  const getTimestamp = (candle: any) => {
    const timestamp = candle.date || candle.timestamp;
    const time = timestamp instanceof Date ? timestamp.getTime() : new Date(timestamp).getTime();
    return Math.floor(time / 1000) as any;
  };

  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 10;

    const initChart = () => {
      if (!data.length) {
        console.log('Chart conditions not met: no data', { dataLength: data.length });
        setIsLoading(false);
        return;
      }

      if (!chartContainerRef.current) {
        retryCount++;
        if (retryCount < maxRetries) {
          console.log(`Chart container not ready, retry ${retryCount}/${maxRetries}`);
          setTimeout(initChart, 200 * retryCount); // Exponential backoff
          return;
        } else {
          console.log('Chart container failed to initialize after max retries');
          setIsLoading(false);
          return;
        }
      }

      // Check if container has dimensions
      const containerRect = chartContainerRef.current.getBoundingClientRect();
      if (containerRect.width === 0 || containerRect.height === 0) {
        retryCount++;
        if (retryCount < maxRetries) {
          console.log(`Chart container has no dimensions, retry ${retryCount}/${maxRetries}`);
          setTimeout(initChart, 200 * retryCount);
          return;
        } else {
          console.log('Chart container has no dimensions after max retries');
          setIsLoading(false);
          return;
        }
      }

      console.log('Chart conditions met:', {
        hasContainer: !!chartContainerRef.current,
        dataLength: data.length,
        containerWidth: containerRect.width,
        containerHeight: containerRect.height
      });

      // Clean up existing chart
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }

      console.log('Initializing chart with data:', data.length, 'points');

      try {
        // Create chart
        const chart = createChart(chartContainerRef.current, {
          width: chartContainerRef.current.clientWidth,
          height: height,
          layout: {
            background: { color: '#1a1a1a' },
            textColor: '#d1d5db',
          },
          grid: {
            vertLines: { color: '#374151' },
            horzLines: { color: '#374151' },
          },
          crosshair: {
            mode: 1,
          },
          rightPriceScale: {
            borderColor: '#485563',
          },
          timeScale: {
            borderColor: '#485563',
            timeVisible: true,
            secondsVisible: false,
          },
        });

        chartRef.current = chart;

        // Add candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
          upColor: '#10b981',
          downColor: '#ef4444',
          borderDownColor: '#ef4444',
          borderUpColor: '#10b981',
          wickDownColor: '#ef4444',
          wickUpColor: '#10b981',
        });

        candlestickSeriesRef.current = candlestickSeries;

        // Convert data to chart format
        const chartData: CandlestickData[] = data.map(candle => ({
          time: getTimestamp(candle),
          open: candle.open,
          high: candle.high,
          low: candle.low,
          close: candle.close,
        }));

        candlestickSeries.setData(chartData);

        // Add volume series if requested
        if (showVolume) {
          const volumeSeries = chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
              type: 'volume',
            },
            priceScaleId: '',
          });

          volumeSeriesRef.current = volumeSeries;

          const volumeData: HistogramData[] = data.map(candle => ({
            time: getTimestamp(candle),
            value: candle.volume,
            color: candle.close >= candle.open ? '#10b981' : '#ef4444',
          }));

          volumeSeries.setData(volumeData);
        }

        // Add technical indicators if available and requested
        if (showIndicators && indicators) {
          addTechnicalIndicators(chart, indicators);
        }

        // Add pattern recognition overlays
        if (indicators?.patternRecognition?.patterns) {
          addPatternOverlays(chart, indicators.patternRecognition.patterns);
        }

        setIsLoading(false);

        // Handle resize
        const handleResize = () => {
          if (chartContainerRef.current && chartRef.current) {
            chartRef.current.applyOptions({
              width: chartContainerRef.current.clientWidth,
            });
          }
        };

        window.addEventListener('resize', handleResize);

        return () => {
          window.removeEventListener('resize', handleResize);
          if (chartRef.current) {
            chartRef.current.remove();
          }
        };
      } catch (error) {
        console.error('Error initializing chart:', error);
        setIsLoading(false);
      }
    };

    // Start the initialization process
    initChart();

    // Cleanup function
    return () => {
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [data, indicators, height, showVolume, showIndicators]);

  const addTechnicalIndicators = (chart: IChartApi, indicators: AdvancedTechnicalIndicators) => {
    // For now, we'll skip adding technical indicators to the price chart
    // as they are better displayed in separate indicator charts
    console.log('Technical indicators available:', indicators);
  };

  const addPatternOverlays = (chart: IChartApi, patterns: any[]) => {
    patterns.forEach(pattern => {
      // Add pattern markers
      if (pattern.type === 'triangle') {
        // Add triangle pattern overlay
        const patternSeries = chart.addLineSeries({
          color: '#8b5cf6',
          lineWidth: 2,
          lineStyle: 2, // Dashed line
        });

        // Create triangle pattern data points
        const patternData: LineData[] = [
          { time: getTimestamp(data[pattern.startIndex]), value: pattern.startPrice },
          { time: getTimestamp(data[pattern.endIndex]), value: pattern.endPrice },
        ];

        patternSeries.setData(patternData);
      }

      if (pattern.type === 'flag') {
        // Add flag pattern overlay
        const flagSeries = chart.addLineSeries({
          color: '#f97316',
          lineWidth: 2,
          lineStyle: 1, // Solid line
        });

        const flagData: LineData[] = [
          { time: getTimestamp(data[pattern.startIndex]), value: pattern.flagTop },
          { time: getTimestamp(data[pattern.endIndex]), value: pattern.flagBottom },
        ];

        flagSeries.setData(flagData);
      }
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-400">Loading {ticker} chart...</span>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div
        ref={chartContainerRef}
        style={{ height }}
        className="w-full bg-gray-900 rounded-lg"
      />
    </div>
  );
};
